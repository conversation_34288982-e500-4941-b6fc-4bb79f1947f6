import { useTheme } from '@/contexts/ThemeContext';

/**
 * Asset Management Hook
 * Provides theme-aware asset paths for logos, images, and other resources
 */
export const useAssets = () => {
  const { currentTheme, isJobSeekerTheme, isEmployerTheme } = useTheme();

  // Base asset paths for each theme
  const assetPaths = {
    jobSeeker: '/assets/job-seeker',
    employer: '/assets/employer'
  };

  // Get the current theme's asset path
  const getAssetPath = (filename: string): string => {
    const basePath = isEmployerTheme ? assetPaths.employer : assetPaths.jobSeeker;
    return `${basePath}/${filename}`;
  };

  // Logo assets - centralized location
  const logos = {
    main: '/assets/logos/logo-main.png',
    dark: '/assets/logos/logo-dark.png',
    // Fallback to root logos if centralized ones don't exist
    fallbackMain: '/logo.png',
    fallbackDark: '/logo-dark.png'
  };

  // Feature images - organized in images directory
  const features = {
    feature1: '/assets/images/job/feature-1.png',
    feature2: '/assets/images/job/feature-2.png',
    feature3: '/assets/images/job/feature-3.png'
  };

  // Hero images
  const hero = {
    background: isEmployerTheme
      ? '/assets/employer/hero-bg.jpg'
      : '/assets/job-seeker/hero-bg.jpg',
    fallbackBackground: '/assets/images/job/header.png'
  };

  // Team photos - organized in images directory
  const team = {
    roger: '/assets/images/team/Roger.jpg',
    bijun: '/assets/images/team/Bijun.jpg',
    yuanwei: '/assets/images/team/Yuanwei.jpg',
    teamPhoto: '/assets/images/team-photo.jpg'
  };

  // Modal and UI assets - organized in images directory
  const ui = {
    modalImage: '/assets/images/modal-image.svg',
    placeholder: '/assets/images/placeholder.svg'
  };

  // Utility function to get logo with error handling
  const getLogo = (variant: 'main' | 'dark' = 'main'): string => {
    return variant === 'main' ? logos.main : logos.dark;
  };

  // Utility function to get logo with fallback
  const getLogoWithFallback = (variant: 'main' | 'dark' = 'main'): string => {
    const logo = getLogo(variant);
    const fallback = variant === 'main' ? logos.fallbackMain : logos.fallbackDark;
    
    // In a real implementation, you might want to check if the file exists
    // For now, we'll return the theme-specific path and let the browser handle fallbacks
    return logo;
  };

  // Theme-specific asset collections
  const themeAssets = {
    jobSeeker: {
      logo: logos.main,
      logoDark: logos.dark,
      primaryColor: '#F59E0B', // Yellow theme
      heroBackground: hero.fallbackBackground
    },
    employer: {
      logo: logos.main,
      logoDark: logos.dark,
      primaryColor: '#0EA5E9', // Blue theme
      heroBackground: hero.background
    }
  };

  // Get current theme's asset collection
  const getCurrentThemeAssets = () => {
    return isEmployerTheme ? themeAssets.employer : themeAssets.jobSeeker;
  };

  // Asset preloading utility
  const preloadAssets = (assetList: string[]) => {
    assetList.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });
  };

  // Get critical assets for preloading
  const getCriticalAssets = (): string[] => {
    const currentAssets = getCurrentThemeAssets();
    return [
      currentAssets.logo,
      currentAssets.logoDark,
      ui.modalImage
    ];
  };

  return {
    // Asset collections
    logos,
    features,
    hero,
    team,
    ui,
    themeAssets,
    
    // Utility functions
    getAssetPath,
    getLogo,
    getLogoWithFallback,
    getCurrentThemeAssets,
    preloadAssets,
    getCriticalAssets,
    
    // Theme info
    currentTheme,
    isJobSeekerTheme,
    isEmployerTheme
  };
};
