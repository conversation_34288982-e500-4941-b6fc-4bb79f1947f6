import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { I18nProvider } from "@/contexts/I18nContext";
import { NavigationProvider } from "@/contexts/NavigationContext";
import { FormTabProvider } from "@/contexts/FormTabContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import JobPage from "./pages/JobPage";
import HiringPage from "./pages/HiringPage";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import PrivacyPolicyDE from "./pages/PrivacyPolicyDE";
import TermsOfService from "./pages/TermsOfService";
import TermsOfServiceDE from "./pages/TermsOfServiceDE";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <I18nProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <NavigationProvider>
              <FormTabProvider>
                <ThemeProvider>
                  <Routes>
                <Route path="/" element={<JobPage />} />
                <Route path="/hiring" element={<HiringPage />} />
                <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                <Route path="/privacy-policy-de" element={<PrivacyPolicyDE />} />
                <Route path="/terms-of-service" element={<TermsOfService />} />
                <Route path="/terms-of-service-de" element={<TermsOfServiceDE />} />
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                  </Routes>
                </ThemeProvider>
              </FormTabProvider>
            </NavigationProvider>
          </BrowserRouter>
        </TooltipProvider>
      </I18nProvider>
    </QueryClientProvider>
  </HelmetProvider>
);

export default App;
