export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  coverImage: string;
  publishedDate: string;
  slug: string;
  language: string;
  tags: string[];
}

export interface BlogContentBlock {
  id: string;
  type: string;
  text?: string;
  level?: number; // for headings
}

export interface BlogDetail {
  id: string;
  title: string;
  excerpt: string;
  slug: string;
  tags: string[];
  language: string;
  publishedDate: string;
  coverImage: string;
  content: BlogContentBlock[];
}

export interface BlogDetailResponse {
  success: boolean;
  blog: BlogDetail | null;
  error?: string;
}

export interface BlogListResponse {
  success: boolean;
  posts: BlogPost[];
  total: number;
  pagination: {
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  error?: string;
}
