import { useI18n } from "@/contexts/I18nContext";

const HiringFeaturesSection = () => {
  const { t } = useI18n();

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-background" aria-labelledby="features-heading">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <header className="text-center mb-16">
          <h2 id="features-heading" className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            {t('features.employer.title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            {t('features.employer.subtitle')}
          </p>
        </header>

        {/* Features Grid */}
        <div className="space-y-24 lg:space-y-32">
          {/* Feature 1 */}
          <article className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div className="space-y-6">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground">
                {t('features.employer.feature1_title')}
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {t('features.employer.feature1_description')}
              </p>
            </div>
            <div className="group relative">
              <div className="relative overflow-hidden rounded-xl transition-smooth">
                <img
                  src="/assets/images/hiring/feature-1.png"
                  alt="AI-powered candidate matching - Find the perfect candidates with intelligent algorithms"
                  className="w-full h-auto shadow-card transition-smooth hover:shadow-soft hover:scale-105"
                />
              </div>
            </div>
          </article>

          {/* Feature 2 */}
          <article className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div className="lg:order-2 space-y-6">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground">
                {t('features.employer.feature2_title')}
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {t('features.employer.feature2_description')}
              </p>
            </div>
            <div className="lg:order-1 group relative">
              <div className="relative overflow-hidden rounded-xl transition-smooth">
                <img
                  src="/assets/images/hiring/feature-2.png"
                  alt="Streamlined hiring process - Efficient recruitment workflow from posting to hiring"
                  className="w-full h-auto shadow-card transition-smooth hover:shadow-soft hover:scale-105"
                />
              </div>
            </div>
          </article>

          {/* Feature 3 */}
          <article className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div className="space-y-6">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground">
                {t('features.employer.feature3_title')}
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {t('features.employer.feature3_description')}
              </p>
            </div>
            <div className="group relative">
              <div className="relative overflow-hidden rounded-xl shadow-card transition-smooth hover:shadow-soft hover:scale-105">
                <img
                  src="/assets/images/hiring/feature-3.png"
                  alt="Quality candidate pool - Access to pre-screened, qualified professionals"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </article>

          {/* Feature 4 */}
          <article className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div className="lg:order-2 space-y-6">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground">
                {t('features.employer.feature4_title')}
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {t('features.employer.feature4_description')}
              </p>
            </div>
            <div className="lg:order-1 group relative">
              <div className="relative overflow-hidden rounded-xl shadow-card transition-smooth hover:shadow-soft hover:scale-105">
                <img
                  src="/assets/images/hiring/feature-4.png"
                  alt="Analytics and insights - Data-driven hiring decisions with comprehensive reporting"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </article>
        </div>
      </div>
    </section>
  );
};

export default HiringFeaturesSection;
