import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calendar, ArrowRight, ExternalLink } from 'lucide-react';
import { useI18n } from '@/contexts/I18nContext';
import { useNavigation } from '@/contexts/NavigationContext';
import { useDesignTokens } from '@/hooks/useDesignTokens';
import { BlogPost } from '@/types/blog';

interface BlogApiResponse {
  posts: BlogPost[];
  total: number;
  success: boolean;
  error?: string;
}

const LatestBlogPosts: React.FC = () => {
  const { t, language } = useI18n();
  const { markInternalNavigation } = useNavigation();
  const { tokens, themeClasses, combineClasses } = useDesignTokens();
  
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Format date according to current language locale
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      const localeMap: Record<string, string> = {
        'en': 'en-US',
        'de': 'de-DE',
        'zh': 'zh-CN',
        'es': 'es-ES'
      };
      
      return date.toLocaleDateString(localeMap[language] || 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Error formatting date:', error);
      return dateString;
    }
  };

  // Fetch blog posts from the API
  useEffect(() => {
    const fetchBlogPosts = async () => {
      setLoading(true);
      setError(null);

      try {
        // Determine the correct API endpoint based on environment
        const isDevelopment = import.meta.env.DEV;
        let apiUrl: string;

        if (isDevelopment) {
          // Use Netlify dev server port for development
          apiUrl = '/.netlify/functions/getBlogs'; // Netlify dev port
        } else {
          apiUrl = '/.netlify/functions/getBlogs';
        }

        const response = await fetch(apiUrl);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data: BlogApiResponse = await response.json();
        
        if (!data.success) {
          throw new Error(data.error || 'Failed to fetch blog posts');
        }
        
        // Filter posts by current language and get latest 3
        const languageFilteredPosts = data.posts
          .filter(post => post.language === language)
          .sort((a, b) => new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime())
          .slice(0, 3);
        
        setPosts(languageFilteredPosts);
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPosts();
  }, [language]);

  // Handle navigation to blog post
  const handlePostClick = () => {
    markInternalNavigation('internal');
  };

  // Handle "View all" navigation
  const handleViewAllClick = () => {
    markInternalNavigation('internal');
  };

  // Loading state
  if (loading) {
    return (
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              {t('blog.latest_posts')}
            </h2>
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-muted-foreground">{t('blog.loading')}</span>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Error state
  if (error) {
    return (
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              {t('blog.latest_posts')}
            </h2>
            <div className="py-12">
              <p className="text-muted-foreground">{t('blog.error')}</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Empty state
  if (posts.length === 0) {
    return (
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              {t('blog.latest_posts')}
            </h2>
            <div className="py-12">
              <p className="text-muted-foreground">{t('blog.no_posts')}</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            {t('blog.latest_posts')}
          </h2>
          <div className="flex justify-center">
            <Link
              to={`/${language}/blog`}
              onClick={handleViewAllClick}
              className={combineClasses(
                "inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors",
                themeClasses.ctaButton
              )}
            >
              {t('blog.view_all')}
              <ArrowRight className="h-4 w-4" />
            </Link>
          </div>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.map((post) => (
            <Link
              key={post.id}
              to={`/blog/${post.slug}`}
              onClick={handlePostClick}
              className={combineClasses(
                tokens.card.elevated,
                "group hover:shadow-lg transition-all duration-300 overflow-hidden block"
              )}
            >
              {/* Cover Image */}
              {post.coverImage && (
                <div className="aspect-video overflow-hidden">
                  <img
                    src={post.coverImage}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    loading="lazy"
                    onError={(e) => {
                      // Hide image if it fails to load
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}

              {/* Content */}
              <div className="p-6">
                {/* Publication Date */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                  <Calendar className="h-4 w-4" />
                  <time dateTime={post.publishedDate}>
                    {t('blog.published_on')} {formatDate(post.publishedDate)}
                  </time>
                </div>

                {/* Title */}
                <h3 className="text-xl font-semibold text-foreground mb-3 line-clamp-2 group-hover:text-primary transition-colors">
                  {post.title}
                </h3>

                {/* Excerpt */}
                <p className="text-muted-foreground mb-4 line-clamp-3">
                  {post.excerpt}
                </p>

                {/* Read More Indicator */}
                <div className="inline-flex items-center gap-2 text-primary group-hover:text-primary/80 font-medium transition-colors">
                  {t('blog.read_more')}
                  <ExternalLink className="h-4 w-4" />
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default LatestBlogPosts;
