import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  date?: Date
  onDateChange?: (date: Date | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  formatString?: string
  mode?: "single" | "month" | "year"
  disablePastDates?: boolean
}

export function DatePicker({
  date,
  onDateChange,
  placeholder = "Pick a date",
  disabled = false,
  className,
  formatString = "PPP",
  mode = "single",
  disablePastDates = false
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false)

  const handleDateSelect = (selectedDate: Date | undefined) => {
    onDateChange?.(selectedDate)
    setOpen(false)
  }

  const getDisplayText = () => {
    if (!date) return placeholder

    switch (mode) {
      case "month":
        return format(date, "yyyy-MM")
      case "year":
        return format(date, "yyyy")
      default:
        return format(date, formatString)
    }
  }

  // Disable past dates if requested
  const getDisabledDates = () => {
    if (disablePastDates) {
      const today = new Date()
      today.setHours(0, 0, 0, 0) // Reset time to start of day
      return { before: today }
    }
    return disabled
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal border border-input hover:bg-background hover:text-foreground",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {getDisplayText()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateSelect}
          disabled={getDisabledDates()}
          initialFocus
          classNames={{
            day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
            day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
          }}
        />
      </PopoverContent>
    </Popover>
  )
}

interface MonthPickerProps {
  date?: Date
  onDateChange?: (date: Date | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function MonthPicker({
  date,
  onDateChange,
  placeholder = "Pick a month",
  disabled = false,
  className
}: MonthPickerProps) {
  const [open, setOpen] = React.useState(false)
  const [currentMonth, setCurrentMonth] = React.useState(date || new Date())

  const handleDateSelect = (selectedDate: Date | undefined) => {
    onDateChange?.(selectedDate)
    setOpen(false)
  }

  const getDisplayText = () => {
    if (!date) return placeholder
    return format(date, "yyyy-MM")
  }

  const navigateYear = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentMonth)
    if (direction === 'prev') {
      newDate.setFullYear(newDate.getFullYear() - 1)
    } else {
      newDate.setFullYear(newDate.getFullYear() + 1)
    }
    setCurrentMonth(newDate)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal border border-input hover:bg-background hover:text-foreground",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {getDisplayText()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3">
          {/* Combined Year and Month navigation */}
          <div className="flex items-center justify-center gap-2 mb-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateYear('prev')}
              className="h-7 w-7 p-0"
              title="Previous year"
            >
              <span className="text-xs">‹‹</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const newDate = new Date(currentMonth)
                newDate.setMonth(newDate.getMonth() - 1)
                setCurrentMonth(newDate)
              }}
              className="h-7 w-7 p-0"
              title="Previous month"
            >
              <ChevronLeft className="h-3 w-3" />
            </Button>
            <div className="text-sm font-medium min-w-[100px] text-center">
              {format(currentMonth, "MMM yyyy")}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const newDate = new Date(currentMonth)
                newDate.setMonth(newDate.getMonth() + 1)
                setCurrentMonth(newDate)
              }}
              className="h-7 w-7 p-0"
              title="Next month"
            >
              <ChevronRight className="h-3 w-3" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateYear('next')}
              className="h-7 w-7 p-0"
              title="Next year"
            >
              <span className="text-xs">››</span>
            </Button>
          </div>

          {/* Calendar */}
          <Calendar
            mode="single"
            selected={date}
            onSelect={handleDateSelect}
            month={currentMonth}
            onMonthChange={setCurrentMonth}
            disabled={disabled}
            initialFocus
            classNames={{
              day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground focus:outline-none focus:ring-0",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none focus:ring-0",
              caption: "hidden", // Hide the default calendar header since we have our own
              nav: "hidden" // Hide the default navigation
            }}
          />
        </div>
      </PopoverContent>
    </Popover>
  )
}
