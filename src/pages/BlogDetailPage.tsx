import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Calendar, Tag, Loader2 } from 'lucide-react';
import { useI18n } from '../contexts/I18nContext';
import { useDesignTokens } from '../hooks/useDesignTokens';
import { BlogDetail, BlogDetailResponse, BlogContentBlock } from '../types/blog';
import Footer from '../components/Footer';
import LanguageSwitcher from '../components/LanguageSwitcher';

// Simple header component for blog detail page
const BlogHeader: React.FC = () => {
  const navigate = useNavigate();

  return (
    <header className="fixed top-0 left-0 right-0 z-[200] backdrop-blur-md bg-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button
              onClick={() => navigate('/')}
              className="flex items-center transition-smooth hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 rounded-lg p-1"
              aria-label="Go to homepage"
            >
              <img
                src="/assets/logos/logo-main.png"
                alt="Sohnus - AI-powered job platform"
                className="h-8 md:h-10 w-auto"
              />
            </button>
          </div>

          {/* Navigation */}
          <div className="flex items-center gap-4">
            <LanguageSwitcher />
          </div>
        </div>
      </div>
    </header>
  );
};

const BlogDetailPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { t, language } = useI18n();
  const { tokens, combineClasses } = useDesignTokens();

  const [blog, setBlog] = useState<BlogDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!slug) {
      setError('Blog post slug is missing');
      setLoading(false);
      return;
    }

    fetchBlogPost(slug);
  }, [slug]);

  const fetchBlogPost = async (postSlug: string) => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = `/.netlify/functions/getBlogBySlug?slug=${encodeURIComponent(postSlug)}`;
      const response = await fetch(apiUrl);

      if (!response.ok) {
        if (response.status === 404) {
          setError(t('blog.not_found'));
        } else {
          setError(t('blog.error'));
        }
        return;
      }

      const data: BlogDetailResponse = await response.json();

      if (!data.success || !data.blog) {
        setError(data.error || t('blog.error'));
        return;
      }

      setBlog(data.blog);
    } catch (error) {
      console.error('Error fetching blog post:', error);
      setError(t('blog.error'));
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(language, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const renderContentBlock = (block: BlogContentBlock, index: number) => {
    const key = `${block.id}-${index}`;

    switch (block.type) {
      case 'paragraph':
        return (
          <p key={key} className="mb-4 text-gray-700 leading-relaxed">
            {block.text}
          </p>
        );
      case 'heading_1':
        return (
          <h1 key={key} className="text-3xl font-bold mb-6 mt-8 text-gray-900">
            {block.text}
          </h1>
        );
      case 'heading_2':
        return (
          <h2 key={key} className="text-2xl font-bold mb-4 mt-6 text-gray-900">
            {block.text}
          </h2>
        );
      case 'heading_3':
        return (
          <h3 key={key} className="text-xl font-bold mb-3 mt-5 text-gray-900">
            {block.text}
          </h3>
        );
      case 'bulleted_list_item':
        return (
          <li key={key} className="mb-2 text-gray-700 ml-4">
            {block.text}
          </li>
        );
      case 'numbered_list_item':
        return (
          <li key={key} className="mb-2 text-gray-700 ml-4">
            {block.text}
          </li>
        );
      default:
        return (
          <div key={key} className="mb-4 text-gray-700">
            {block.text}
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <BlogHeader />
        <main className="container mx-auto px-4 py-16 pt-24">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
              <p className="text-gray-600">{t('blog.loading')}</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !blog) {
    return (
      <div className="min-h-screen bg-background">
        <BlogHeader />
        <main className="container mx-auto px-4 py-16 pt-24">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error === t('blog.not_found') ? t('blog.not_found') : t('blog.error')}
            </h1>
            <p className="text-gray-600 mb-8">
              {error === t('blog.not_found')
                ? t('blog.not_found_description')
                : t('blog.error_description')
              }
            </p>
            <Link
              to="/"
              className={combineClasses(
                tokens.button.primary,
                "inline-flex items-center gap-2 px-6 py-3 rounded-lg transition-colors"
              )}
            >
              <ArrowLeft className="h-4 w-4" />
              {t('blog.back_to_home')}
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <BlogHeader />
      <main className="container mx-auto px-4 py-8 pt-24">
        {/* Back Navigation */}
        <div className="mb-8">
          <button
            onClick={() => navigate(-1)}
            className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('blog.back')}
          </button>
        </div>

        {/* Blog Content */}
        <article className="max-w-4xl mx-auto">
          {/* Cover Image */}
          {blog.coverImage && (
            <div className="mb-8">
              <img
                src={blog.coverImage}
                alt={blog.title}
                className="w-full h-64 md:h-96 object-cover rounded-lg"
              />
            </div>
          )}

          {/* Blog Header */}
          <header className="mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {blog.title}
            </h1>

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(blog.publishedDate)}</span>
              </div>

              {blog.tags && blog.tags.length > 0 && (
                <div className="flex items-center gap-1">
                  <Tag className="h-4 w-4" />
                  <span>{blog.tags.join(', ')}</span>
                </div>
              )}
            </div>

            {blog.excerpt && (
              <p className="text-lg text-gray-600 leading-relaxed">
                {blog.excerpt}
              </p>
            )}
          </header>

          {/* Blog Content */}
          <div className="prose prose-lg max-w-none">
            {blog.content && blog.content.map((block, index) => renderContentBlock(block, index))}
          </div>
        </article>
      </main>
      <Footer />
    </div>
  );
};

export default BlogDetailPage;
