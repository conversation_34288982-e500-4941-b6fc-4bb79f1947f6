{"common": {"required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "submitting": "Wird gesendet...", "success": "Erfolgreich!", "error": "<PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON>", "loading": "Lädt...", "select_placeholder": "Bitte auswählen...", "language": "<PERSON><PERSON><PERSON>", "optional": "optional", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "remove": "Entfernen"}, "navigation": {"home": "Startseite", "privacy_policy": "Datenschutzerklärung", "terms_of_service": "Nutzungsbedingungen", "toggle_hiring": "<PERSON><PERSON><PERSON> Arbeitgeber", "toggle_job_seeking": "Stellensuche", "toggle_hiring_short": "Einstellen", "toggle_job_seeking_short": "Jobs"}, "hero": {"title": "<PERSON><PERSON><PERSON>.", "title_powered": "Von KI angetrieben.", "subtitle": "Überspring den Papierkram. Hol dir den Job.", "subtitle_2": "<PERSON><PERSON><PERSON>, ein<PERSON>ch und in deiner Sprache.", "cta_worker": "<PERSON><PERSON><PERSON> beit<PERSON>", "cta_company": "Personal finden", "trust_indicator": "2.500+ <PERSON><PERSON><PERSON><PERSON>", "employer": {"title": "Top-Talente finden.", "title_powered": "Von KI angetrieben.", "subtitle": "Finden Sie über 1.000 verifizierte Fachkräfte in Ihrer Nähe – mit persönlicher Unterstützung von uns in jedem Schritt.", "subtitle_2": "<PERSON><PERSON><PERSON>, zuverlässig und qualitätsgeprüft.", "cta_worker": "Stellenausschreibung", "cta_company": "<PERSON><PERSON> er<PERSON>"}}, "features": {"title": "Warum Sohnus wählen?", "subtitle": "Wir machen Job-Matching einfach und effektiv", "section_title": "Warum Sohnus wählen?", "section_subtitle": "Entdecken Si<PERSON>, wie unsere KI-gestützte Plattform die Jobsuche und Einstellung revolutioniert", "feature1_title": "<PERSON> chattest, wir machen den Rest", "feature1_description": "<PERSON><PERSON>us hört auf deine Stimme, versteht was du gut kannst und erstellt dein eigenes Profil.", "feature2_title": "<PERSON><PERSON><PERSON> Jobs finden", "feature2_description": "Du musst nicht stundenlang suchen. Unsere KI findet die besten passenden Jobs und bewirbt sich sogar für dich.", "feature3_title": "Eine bessere Zukunft", "feature3_description": "Mit mehr Erfahrung zeigen wir dir besser bezahlte Jobs, stabilere Arbeitszeiten und Rollen mit mehr Verantwortung.", "feature4_title": "Professionelle Unterstützung", "feature4_description": "Erhalten Sie fachkundige Beratung während Ihrer Jobsuche mit personalisierter Karriereberatung und Vorstellungsgesprächsvorbereitung.", "employer": {"title": "Warum wählen Arbeitgeber Sohnus?", "subtitle": "Wir machen die Einstellung qualifizierter Arbeiter einfach und effektiv", "feature1_title": "Intelligente <PERSON>", "feature1_description": "Unsere KI analysiert Stellenanforderungen und Arbeiterprofile, um die perfekte Übereinstimmung für Ihre spezifischen Bedürfnisse zu finden.", "feature2_title": "Schneller Einstellungsprozess", "feature2_description": "Reduzieren Sie Ihre Einstellungszeit von Wochen auf Tage. Erhalten Sie qualifizierte Kandidaten innerhalb von 24 Stunden.", "feature3_title": "Vorab verifizierte Arbeiter", "feature3_description": "Alle Arbeiter durchlaufen gründliche Hintergrundprüfungen und Qualifikationsverifikationen, bevor sie unserer Plattform beitreten.", "feature4_title": "Analysen & Einblicke", "feature4_description": "Treffen Sie datengestützte Einstellungsentscheidungen mit umfassenden Berichten und Analysen zu Ihrer Rekrutierungsleistung."}}, "forms": {"title": "Starten Sie noch heute", "subtitle": "Wählen Sie die richtige Option für Sie: Suchen Sie einen Job oder sind Si<PERSON> ein Unternehmen, das qualifizierte Fachkräfte sucht?", "tab_worker": "Ich suche einen Job", "tab_company": "Wir stellen ein", "required_note": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "footer_agreement": "Mit dem Absenden dieses Formulars stimmen Sie unseren", "and": "und"}, "chatbot": {"title": "Job Assistent", "chat_with_us": "Mit uns chatten", "chat": "Cha<PERSON>"}, "worker_form": {"title": "Bewerbung für Arbeitssuchende", "subtitle": "Füllen Sie die folgenden Felder aus. Wir erstellen automatisch Ihren Lebenslauf aus diesen Informationen.", "success_title": "Bewerbung erfolgreich eingereicht!", "success_description": "Wir melden uns bald bei Ihnen. Sie erhalten auch in Kürze eine Bestätigungs-E-Mail.", "personal_info": "Persönliche Informationen", "job_expectations": "Berufliche Erwartungen", "work_history": "Berufserfahrung", "skills_qualifications": "Fähigkeiten & Qualifikationen", "fields": {"first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "email": "E-Mail-Adresse", "phone": "Telefonnummer", "connect_whatsapp": "Über WhatsApp verbinden", "facebook": "Facebook-<PERSON><PERSON>", "connect_facebook": "Über Facebook verbinden", "wechat": "WeChat-Konto", "connect_wechat": "WeChat-Gruppe beitreten", "city": "Aktuelle Stadt", "nationality": "Nationalität", "avatar": "Profilfoto", "availability_date": "Verfügbarkeitsdatum", "willing_to_travel": "Reisebereitschaft", "salary_expectation": "Gehaltsvorstellung (€/Monat)", "desired_position": "Gewünschte Position", "employment_type": "Anstellungsart", "job_title": "Berufsbezeichnung", "job_company": "Unternehmen", "job_start": "Startdatum", "job_end": "Enddatum", "job_duration": "<PERSON><PERSON>", "job_tasks": "Hauptaufgaben & Verantwortlichkeiten", "job1_title": "Letzte Position", "job1_company": "Letztes Unternehmen", "job1_duration": "<PERSON><PERSON>", "job1_tasks": "Hauptaufgaben & Verantwortlichkeiten", "education_level": "Bildungsstand", "education_start": "Startdatum", "education_end": "Enddatum", "education_school": "Schule/Universität", "education_detail": "Abschluss/Studiengang Details", "german_level": "Deutschkenntnisse", "other_languages": "<PERSON><PERSON><PERSON>", "driving_license": "<PERSON><PERSON><PERSON><PERSON>", "skills_certs": "Zusätzliche Fähigkeiten & Zertifikate"}, "job_entry": "Berufserfahrung", "add_job": "Weitere Stelle hinzufügen", "education_entry": "Bildung", "add_education": "Weitere Bildung hinzufügen", "education_details": "Bildungsdetails", "avatar_help": "Professionelles Foto hochladen (max. 2MB, JPG/PNG)", "avatar_required": "Profilfoto ist erforderlich", "avatar_invalid_format": "Bitte laden Si<PERSON> eine gültige Bilddatei hoch (JPG, PNG)", "avatar_too_large": "Bilddatei muss kleiner als 2MB sein", "avatar_too_small": "Bilddatei ist zu klein. Bitte laden Si<PERSON> ein gültiges Bild hoch", "placeholders": {"first_name": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Vornamen ein", "last_name": "<PERSON><PERSON><PERSON> Sie Ihren Nachnamen ein", "email": "<EMAIL>", "phone": "+49 **********", "facebook": "facebook.com/ihrprofil oder @ihrhandle", "wechat": "Ihre WeChat-ID", "city": "z.B. Berlin", "nationality": "<PERSON>.<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Polnisch", "willing_to_travel": "Reisebereitschaft auswählen", "salary_expectation": "z.B. 2500", "desired_position": "z.B. <PERSON>arbei<PERSON>", "employment_type": "Anstellungsart auswählen", "job_title": "z.B. Kommissionierer", "job_company": "z.B. Amazon Logistik", "job_duration": "z.B. 2 Jahre", "job_tasks": "Beschreiben Sie Ihre Hauptverantwortlichkeiten... (max 300 Zeichen)", "job1_title": "z.B. Kommissionierer", "job1_company": "z.B. Amazon Logistik", "job1_duration": "z.B. 2 Jahre", "job1_tasks": "Beschreiben Sie Ihre Hauptverantwortlichkeiten... (max 300 Zeichen)", "education_level": "Bildungsstand auswählen", "education_school": "z.B. Universität Berlin", "education_detail": "z.B. Bachelor In<PERSON><PERSON><PERSON>, Master Ingenieurwesen... (max 200 Zeichen)", "german_level": "Deutschlevel auswählen", "other_languages": "<PERSON><PERSON><PERSON><PERSON> (C1), <PERSON><PERSON><PERSON> (A2) (max 100 Zeichen)", "driving_license": "Führerscheinklasse auswählen", "skills_certs": "<PERSON><PERSON><PERSON><PERSON>, SCC-Zertifikat, MS Office... (max 250 Zeichen)", "availability_date": "Verfügbarkeitsdatum auswählen", "job_start_month": "Startmonat auswählen", "job_end_month": "Endmonat auswählen", "education_start_month": "Startmonat auswählen", "education_end_month": "Endmonat auswählen"}, "options": {"employment_type": {"full_time": "Vollzeit", "part_time": "Teilzeit", "contract": "Zeitarbeit", "temporary": "<PERSON><PERSON><PERSON><PERSON>", "internship": "Praktikum"}, "education_level": {"no_degree": "<PERSON><PERSON>", "vocational_training": "Berufsausbildung", "high_school": "Abitur", "bachelors_degree": "Bachelor-<PERSON><PERSON><PERSON><PERSON>s", "masters_degree": "Master-<PERSON><PERSON><PERSON><PERSON><PERSON>", "phd": "Promotion"}, "german_level": {"a1": "A1 (Anf<PERSON>nger)", "a2": "A2 (Grundkenntnisse)", "b1": "B1 (Mittelstufe)", "b2": "B2 (G<PERSON> Mittelstufe)", "c1": "C1 (Fortgeschritten)", "c2": "C2 (<PERSON><PERSON> gut)", "native_speaker": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "driving_license": {"none": "<PERSON><PERSON>", "class_b": "Klasse B (PKW)", "class_c1": "Klasse C1 (Kleiner LKW)", "class_ce": "Klasse CE (LKW mit Anhänger)", "forklift_license": "Staplerschein"}}}, "team": {"title": "Unser Team", "subtitle": "Wir sind eine vielfältige Gruppe von Innovatoren, Ingenieuren und Karriereexperten, die sich der Revolution der Arbeitsplatzsuche verschrieben haben.", "hero_title": "Innovation durch Zusammenarbeit", "hero_subtitle": "Unser Team arbeitet gemeinsam daran, die Zukunft der Arbeit zu gestalten – eine Verbindung nach der anderen.", "members": {"roger": {"name": "<PERSON>", "role": "CEO & <PERSON><PERSON><PERSON><PERSON>", "bio": "Mit einem Hintergrund in Ingenieurwesen und Management baut <PERSON>f, um <PERSON>-<PERSON><PERSON> schneller, fairer und einfacher zu machen – mit KI, die Arbeitnehmer und Unternehmen ohne Sprachbarrieren verbindet."}, "bijun": {"name": "Dr. <PERSON><PERSON><PERSON>", "role": "CTO & Mitgründerin", "bio": "Mit einem Doktortitel in Ingenieurwesen und umfassender Erfahrung in fortschrittlichen Systemen leitet Bijun Sohnus' technische Vision. Sie entwickelt und skaliert die KI-Plattform, die automatisches Job-Matching ermöglicht und Sprachlücken überbrückt."}, "yuanwei": {"name": "<PERSON><PERSON> Fang", "role": "CPO & Mitgründerin", "bio": "Yuanwei kombiniert tiefe UX-Expertise mit praktischer Erfahrung in der Gestaltung von KI-Chat-Erlebnissen. Sie leitet Sohnus' technisches Design und stellt sicher, dass unsere Job-Matching-Oberfläche einfach, hilfreich und für echte Nutzer entwickelt ist."}}}, "footer": {"company_description": "Wir machen sinnvolle Beschäftigung für jeden Arbeiter durch KI-gestütztes Job-Matching zugänglich.", "job_seekers": "Für Arbeitssuchende", "employers": "<PERSON><PERSON><PERSON> Arbeitgeber", "contact_info": "Kontaktinformationen", "legal": "Rechtliches", "apply_now": "Jetzt bewerben", "post_jobs": "<PERSON>s ausschreiben", "privacy_policy": "Datenschutzerklärung", "terms_of_service": "Nutzungsbedingungen", "copyright": "© 2025 Sohnus. Alle Rechte vorbehalten."}, "company_form": {"title": "Unternehmensanfrage", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON> uns von Ihrem Unternehmen und Ihren Personalanforderungen", "success_title": "Anfrage erfolgreich eingereicht!", "success_description": "Wir melden uns bald bei Ihnen. Sie erhalten auch in Kürze eine Bestätigungs-E-Mail.", "fields": {"company_name": "Firmenname", "contact_person": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contact_email": "Kontakt-E-Mail", "contact_phone": "Kontakt-Telefon", "company_website": "Firmen-Website", "needed_positions": "Benötigte Positionen", "number_of_vacancies": "<PERSON><PERSON><PERSON> der Stellen", "work_location": "Arbeitsort", "required_skills": "Erforderliche Fähigkeiten", "employment_model": "An<PERSON><PERSON>ungsmodell", "urgency": "Dringlichkeit", "job_description": "Stellenbeschreibung"}, "placeholders": {"company_name": "Ihr Firmenname", "contact_person": "Vollständiger Name der Kontaktperson", "contact_email": "<EMAIL>", "contact_phone": "+49 **********", "company_website": "https://www.firma.de", "needed_positions": "<PERSON><PERSON><PERSON><PERSON>, Staplerfahrer", "number_of_vacancies": "z.B. 5", "work_location": "z.B. Berlin, Deutschland oder Remote", "required_skills": "<PERSON><PERSON><PERSON><PERSON>, Deutsch B2, Körperliche Fitness", "employment_model": "Anstellungsart auswählen", "urgency": "Dringlichkeitsstufe auswählen", "job_description": "Detaillierte Beschreibung der Stellenverantwortlichkeiten, Arbeitsumgebung, Vorteile und andere relevante Informationen..."}, "options": {"employment_model": {"full_time": "Vollzeit", "part_time": "Teilzeit", "contract": "Zeitarbeit", "temporary": "<PERSON><PERSON><PERSON><PERSON>", "internship": "Praktikum"}, "urgency": {"immediate": "Sofort (innerhalb 1 Woche)", "urgent": "Dringend (innerhalb 2-4 Wochen)", "normal": "Normal (innerhalb 1-2 Monaten)", "flexible": "Flexibel (wenn richtiger Kandidat gefunden wird)"}}}, "welcome_modal": {"text_over_image": "Beginnen Sie Ihre Reise hier", "title": "Will<PERSON>mmen bei Sohnus", "description": "Wählen Sie Ihren Weg, um mit unserer KI-gestützten Jobplattform zu beginnen", "job_seeker_button": "Ich suche einen Job", "hiring_button": "Wir stellen ein", "additional_info": "<PERSON><PERSON> kö<PERSON> dies später in Ihren Profileinstellungen ändern", "close_button": "Modal schließen"}, "blog": {"latest_posts": "Neueste Blog-Beiträge", "view_all": "Alle anzeigen →", "loading": "Blog-Beiträge werden geladen...", "error": "Blog-Beiträge können derzeit nicht geladen werden. Bitte versuchen Sie es später erneut.", "no_posts": "Noch keine Blog-Beiträge in Ihrer Sprache verfügbar.", "read_more": "Wei<PERSON>lesen", "published_on": "Veröffentlicht am", "not_found": "Blog-Beitrag nicht gefunden", "not_found_description": "Der gesuchte Blog-Beitrag existiert nicht oder wurde entfernt.", "error_description": "<PERSON>im <PERSON> des Blog-Beitrags ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.", "back_to_home": "Zurück zur Startseite", "back": "Zurück"}}