{"common": {"required": "必填字段", "submit": "提交", "submitting": "提交中...", "success": "成功！", "error": "错误", "warning": "警告", "loading": "加载中...", "select_placeholder": "请选择...", "language": "语言", "optional": "可选", "yes": "是", "no": "否", "remove": "删除"}, "navigation": {"home": "首页", "privacy_policy": "隐私政策", "terms_of_service": "服务条款", "toggle_hiring": "招聘", "toggle_job_seeking": "求职", "toggle_hiring_short": "招聘", "toggle_job_seeking_short": "求职"}, "hero": {"title": "为蓝领服务", "title_powered": "由AI驱动", "subtitle": "跳过繁琐的文书，找到工作", "subtitle_2": "快速、简单，使用你的语言", "cta_worker": "免费加入", "cta_company": "招聘人才", "trust_indicator": "2,500+ 求职者", "employer": {"title": "寻找顶尖人才", "title_powered": "由AI驱动", "subtitle": "与您所在地区 1,000 多位经过验证的蓝领工人建立联系，我们将在每一步提供贴心支持。", "subtitle_2": "快速、可靠、质量认证", "cta_worker": "发布职位", "cta_company": "了解更多"}}, "features": {"title": "为什么选择<PERSON>hnus？", "subtitle": "我们让工作匹配变得简单有效", "section_title": "为什么选择<PERSON>hnus？", "section_subtitle": "了解我们的AI驱动平台如何革新求职和招聘", "feature1_title": "你只需聊天，我们来处理后续", "feature1_description": "Sohnus倾听你的声音，了解你的专长，并创建你的个人档案", "feature2_title": "匹配合适的工作", "feature2_description": "无需再花费数小时搜索，我们的AI会找到最匹配的工作，甚至为你申请", "feature3_title": "更美好的未来", "feature3_description": "随着经验的增加，我们为你展示薪酬更高的工作、更稳定的工作时间和更多责任的职位", "feature4_title": "专业支持", "feature4_description": "在求职过程中获得专业指导，包括个性化职业建议和面试准备", "employer": {"title": "为什么雇主选择Sohnus？", "subtitle": "我们让招聘技能工人变得简单有效", "feature1_title": "智能候选人匹配", "feature1_description": "我们的AI分析职位要求和工人档案，为您的特定需求找到完美匹配。", "feature2_title": "快速招聘流程", "feature2_description": "将您的招聘时间从几周缩短到几天。在24小时内获得合格候选人。", "feature3_title": "预先验证的工人", "feature3_description": "所有工人在加入我们的平台之前都经过彻底的背景调查和技能验证。", "feature4_title": "分析与洞察", "feature4_description": "通过全面的招聘绩效报告和分析，做出数据驱动的招聘决策"}}, "forms": {"title": "现在加入", "subtitle": "选择适合的选项：你是在找工作还是公司在寻找合格的专业人员？", "tab_worker": "我在找工作", "tab_company": "我们在招聘", "required_note": "必填字段", "footer_agreement": "提交此表单即表示你同意我们的", "and": "和"}, "chatbot": {"title": "求职助手", "chat_with_us": "与我们聊天", "chat": "聊天"}, "worker_form": {"title": "求职者申请", "subtitle": "填写以下字段。我们将根据这些信息自动创建简历", "success_title": "申请提交成功！", "success_description": "你会很快收到确认邮件，我们很快会与你联系。", "personal_info": "个人信息", "job_expectations": "工作期望", "work_history": "工作经历", "skills_qualifications": "技能与资格", "fields": {"first_name": "名", "last_name": "姓", "email": "电子邮箱", "phone": "手机号", "connect_whatsapp": "通过WhatsApp联系", "facebook": "Facebook账号", "connect_facebook": "通过Facebook联系", "wechat": "微信号", "connect_wechat": "加入微信群", "city": "当前城市", "nationality": "国籍", "avatar": "头像照片", "availability_date": "可入职日期", "willing_to_travel": "出差意愿", "salary_expectation": "期望薪资 (€/月)", "desired_position": "期望职位", "employment_type": "雇佣类型", "job_title": "职位名称", "job_company": "公司", "job_start": "开始日期", "job_end": "结束日期", "job_duration": "工作时长", "job_tasks": "主要任务与职责", "job1_title": "最近职位", "job1_company": "最近公司", "job1_duration": "工作时长", "job1_tasks": "主要任务与职责", "education_level": "教育水平", "education_start": "开始日期", "education_end": "结束日期", "education_school": "学校/大学", "education_detail": "学位/专业详情", "german_level": "德语水平", "other_languages": "其他语言", "driving_license": "驾驶执照", "skills_certs": "其他技能与证书"}, "job_entry": "工作经历", "add_job": "添加工作经历", "education_entry": "教育经历", "add_education": "添加教育经历", "education_details": "教育详情", "avatar_help": "上传专业照片（最大2MB，JPG/PNG格式）", "avatar_required": "头像照片是必需的", "avatar_invalid_format": "请上传有效的图片文件（JPG、PNG格式）", "avatar_too_large": "图片文件必须小于2MB", "avatar_too_small": "图片文件太小，请上传有效的图片", "placeholders": {"first_name": "输入名字", "last_name": "输入姓氏", "email": "<EMAIL>", "phone": "+49 **********", "facebook": "facebook.com/你的用户名 或 @你的账号", "wechat": "你的微信号", "city": "例如 柏林", "nationality": "例如 中国, 德国, 波兰", "willing_to_travel": "选择出差意愿", "salary_expectation": "例如 2500", "desired_position": "例如 仓库工人", "employment_type": "选择雇佣类型", "job_title": "例如 拣货员", "job_company": "例如 亚马逊物流", "job_duration": "例如 2年", "job_tasks": "描述主要职责... (最多300字符)", "job1_title": "例如 拣货员", "job1_company": "例如 亚马逊物流", "job1_duration": "例如 2年", "job1_tasks": "描述主要职责... (最多300字符)", "education_level": "选择教育水平", "education_school": "例如 北京大学", "education_detail": "例如 计算机科学学士, 工程硕士... (最多200字符)", "german_level": "选择德语水平", "other_languages": "例如 英语 (C1), 西班牙语 (A2) (最多100字符)", "driving_license": "选择驾照类型", "skills_certs": "例如 焊接证书, SCC证书, MS Office... (最多250字符)", "availability_date": "选择可入职日期", "job_start_month": "选择开始月份", "job_end_month": "选择结束月份", "education_start_month": "选择开始月份", "education_end_month": "选择结束月份"}, "options": {"employment_type": {"full_time": "全职", "part_time": "兼职", "contract": "合同工", "temporary": "临时工", "internship": "实习"}, "education_level": {"no_degree": "无学位", "vocational_training": "职业培训", "high_school": "高中", "bachelors_degree": "学士学位", "masters_degree": "硕士学位", "phd": "博士学位"}, "german_level": {"a1": "A1 (初学者)", "a2": "A2 (基础)", "b1": "B1 (中级)", "b2": "B2 (中高级)", "c1": "C1 (高级)", "c2": "C2 (精通)", "native_speaker": "母语"}, "driving_license": {"none": "无驾照", "class_b": "B类 (汽车)", "class_c1": "C1类 (小卡车)", "class_ce": "CE类 (卡车带拖车)", "forklift_license": "叉车执照"}}}, "team": {"title": "团队介绍", "subtitle": "我们是一个多元化的创新者、工程师和职业专家团队，致力于革新人们寻找有意义工作的方式。", "hero_title": "协作创新", "hero_subtitle": "我们的团队携手合作，构建工作的未来，一次连接一个机会。", "members": {"roger": {"name": "<PERSON>", "role": "首席执行官兼创始人", "bio": "凭借工程和管理背景，Roger正在构建Sohnus，让工作匹配更快速、更公平、更简单——使用AI连接工人和公司，消除语言障碍。"}, "bijun": {"name": "Dr. <PERSON><PERSON><PERSON>", "role": "首席技术官兼联合创始人", "bio": "拥有工程博士学位和丰富的系统经验，Bijun领导Sohnus的技术愿景。她构建和扩展支持自动工作匹配的AI平台——弥合语言差距，高效连接工人和雇主。"}, "yuanwei": {"name": "<PERSON><PERSON> Fang", "role": "首席产品官兼联合创始人", "bio": "Yuanwei结合深厚的用户体验专业知识和AI聊天体验的实践经验。她领导Sohnus的技术设计，确保我们的工作匹配界面简单、有用——为真实用户而构建。"}}}, "footer": {"company_description": "通过AI驱动的工作匹配，让每个蓝领工人都能获得有意义的就业机会。", "job_seekers": "求职者", "employers": "雇主", "contact_info": "联系信息", "legal": "法律条款", "apply_now": "立即申请", "post_jobs": "发布职位", "privacy_policy": "隐私政策", "terms_of_service": "服务条款", "copyright": "© 2025 Sohnus. 保留所有权利。"}, "company_form": {"title": "公司咨询", "subtitle": "告诉我们公司和人员需求", "success_title": "咨询提交成功！", "success_description": "我们很快会与你联系。你也会很快收到确认邮件。", "fields": {"company_name": "公司名称", "contact_person": "联系人", "contact_email": "联系邮箱", "contact_phone": "联系电话", "company_website": "公司网站", "needed_positions": "所需职位", "number_of_vacancies": "职位数量", "work_location": "工作地点", "required_skills": "所需技能", "employment_model": "雇佣模式", "urgency": "紧急程度", "job_description": "职位描述"}, "placeholders": {"company_name": "公司名称", "contact_person": "联系人全名", "contact_email": "<EMAIL>", "contact_phone": "+49 **********", "company_website": "https://www.company.com", "needed_positions": "例如 仓库工人, 叉车司机", "number_of_vacancies": "例如 5", "work_location": "例如 德国柏林 或 远程", "required_skills": "例如 叉车执照, 德语B2水平, 身体健康", "employment_model": "选择雇佣类型", "urgency": "选择紧急程度", "job_description": "详细描述工作职责、工作环境、福利和其他相关信息..."}, "options": {"employment_model": {"full_time": "全职", "part_time": "兼职", "contract": "合同工", "temporary": "临时工", "internship": "实习"}, "urgency": {"immediate": "立即 (1周内)", "urgent": "紧急 (2-4周内)", "normal": "正常 (1-2个月内)", "flexible": "灵活 (找到合适候选人时)"}}}, "welcome_modal": {"text_over_image": "从这里开始您的旅程", "title": "欢迎来到<PERSON><PERSON><PERSON>", "description": "选择您的路径，开始使用我们的AI驱动求职平台", "job_seeker_button": "我在找工作", "hiring_button": "我们在招聘", "additional_info": "您可以稍后在个人资料设置中更改此选项", "close_button": "关闭弹窗"}}