#!/usr/bin/env node

/**
 * Test script for chatbot functionality
 * Usage: node test-chatbot.js
 */

const STAGING_URL = 'https://dev--sohnus.netlify.app';

// Test data for chatbot submissions (simplified contact info)
const chatbotTestData = {
  english_phone: {
    FirstName: 'Chatbot',
    LastName: 'User',
    PhoneNumber: '+49 123 456789',
    ConnectWhatsapp: 'yes',
    Facebook: '',
    ConnectFacebook: 'no',
    Wechat_ZH: '',
    ConnectWechat_ZH: 'no',
    Status: 'Chatbot',
    Language: 'en',
    formType: 'worker'
  },

  german_facebook: {
    FirstName: 'Chatbot',
    LastName: 'User',
    PhoneNumber: '',
    ConnectWhatsapp: 'no',
    Facebook: 'maria.schmidt.berlin',
    ConnectFacebook: 'yes',
    Wechat_ZH: '',
    ConnectWechat_ZH: 'no',
    Status: 'Chatbot',
    Language: 'de',
    formType: 'worker'
  },

  chinese_wechat: {
    FirstName: 'Chatbot',
    LastName: 'User',
    PhoneNumber: '',
    ConnectWhatsapp: 'no',
    Facebook: '',
    ConnectFacebook: 'no',
    Wechat_ZH: 'xiaoming_wang_2024',
    ConnectWechat_ZH: 'yes',
    Status: 'Chatbot',
    Language: 'zh',
    formType: 'worker'
  },

  spanish_phone: {
    FirstName: 'Chatbot',
    LastName: 'User',
    PhoneNumber: '+49 162 55667788',
    ConnectWhatsapp: 'yes',
    Facebook: '',
    ConnectFacebook: 'no',
    Wechat_ZH: '',
    ConnectWechat_ZH: 'no',
    Status: 'Chatbot',
    Language: 'es',
    formType: 'worker'
  }
};

async function testChatbotSubmission(testName, data) {
  console.log(`\n🤖 Testing Chatbot ${testName}...`);
  
  try {
    const response = await fetch(`${STAGING_URL}/.netlify/functions/submit-form`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log(`✅ Chatbot ${testName} - SUCCESS`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Status: ${data.Status}`);
      console.log(`   Language: ${data.Language}`);
      console.log(`   Name: ${data.FirstName} ${data.LastName}`);
      console.log(`   Phone: ${data.PhoneNumber}`);
      console.log(`   WhatsApp: ${data.ConnectWhatsapp}`);
      console.log(`   Facebook: ${data.ConnectFacebook}`);
      if (data.Language === 'zh') {
        console.log(`   WeChat: ${data.ConnectWechat_ZH}`);
      }
    } else {
      console.log(`❌ Chatbot ${testName} - FAILED`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Error:`, result);
    }
  } catch (error) {
    console.log(`💥 Chatbot ${testName} - ERROR`);
    console.log(`   Error:`, error.message);
  }
}

async function runChatbotTests() {
  console.log(`🚀 Testing Chatbot on staging: ${STAGING_URL}`);
  console.log('=' .repeat(60));

  // Test all scenarios
  await testChatbotSubmission('English (Phone)', chatbotTestData.english_phone);
  await testChatbotSubmission('German (Facebook)', chatbotTestData.german_facebook);
  await testChatbotSubmission('Chinese (WeChat)', chatbotTestData.chinese_wechat);
  await testChatbotSubmission('Spanish (Phone)', chatbotTestData.spanish_phone);

  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Chatbot Testing completed!');
  console.log('\n📋 Manual Chatbot Testing Checklist:');
  console.log('1. Visit: https://dev--sohnus.netlify.app/');
  console.log('2. Wait 12 seconds for auto-popup OR click "Chat with us" button');
  console.log('3. Test the conversational flow:');
  console.log('   - Greeting message appears');
  console.log('   - Choose "Create CV" (redirects to form) or "Chat more"');
  console.log('   - For "Chat more": choose direct contact or leave info');
  console.log('   - Direct contact opens WhatsApp/Facebook');
  console.log('   - Leave info: enter phone/Facebook/WeChat');
  console.log('   - Confirmation message appears');
  console.log('4. Test in different languages (EN/DE/ZH/ES)');
  console.log('5. Chinese users see WeChat options');
  console.log('6. Verify data is stored with Source="Chatbot"');
  console.log('7. Check auto-popup timing (12 seconds)');
  console.log('8. Test personality: sunny, helpful tone');
}

// Run the tests
runChatbotTests().catch(console.error);
